# Инструкция по использованию скрипта парсинга JSON в Google Sheets

## Что делает скрипт

Скрипт читает JSON данные из столбца A вашей Google Sheets таблицы, парсит их и создает сводную таблицу с:
- Суммами по валютам (EUR, USD, RUB) для каждого магазина
- Отдельным учетом сумм с статусом `"payout": false` (не выплачено)
- Подсчетом по ID платежей (ID№) из поля `finance.info` (только для выплаченных)
- Общей статистикой по выплаченным и не выплаченным суммам
- Возможностью исключать определенные магазины из обработки

## Новые возможности

### 🚫 Управление исключениями магазинов
- Создается отдельный лист "Исключения" для управления списком магазинов
- Магазины в списке исключений не обрабатываются и не попадают в результаты
- Можно легко добавлять/удалять магазины из обработки

### 💰 Фильтрация по статусу выплат
- **Исключает записи с `"payout": false`** из основной таблицы и подсчета ID
- Только записи с `"payout": true` попадают в детализацию по магазинам
- Все записи учитываются в сводной статистике для анализа

### 📊 Сводная статистика
- **Строка 3-6**: Общая статистика по всем данным:
  - Выплачено (payout: true)
  - Не выплачено (payout: false)
  - Общая сумма по всем записям
- **Строка 10+**: Детализация по магазинам (только payout: true)

### 💳 Подсчет по ID платежей
- Автоматически находит ID платежей в формате `ID№xxxxx` в поле `finance.info`
- Группирует суммы по каждому уникальному ID платежу
- **ВАЖНО**: ID платежи учитываются только для записей с `"payout": true` или без указания статуса
- Записи с `"payout": false` не включаются в ID платежи (деньги еще не выплачены)
- Выводит результаты в отдельных столбцах с разбивкой по валютам

### 💰 Учет статуса выплат
- Отдельные столбцы для сумм с `"payout": false` (не выплачено)
- Общая статистика на 8-9 строках с итогами по статусам выплат
- Основная таблица начинается с 10 строки
- **Учитывает только записи с `"payout": true`**

## Установка скрипта

1. Откройте вашу Google Sheets таблицу
2. Перейдите в меню **Расширения** → **Apps Script**
3. Удалите весь код в редакторе (если есть)
4. Скопируйте и вставьте код из файла `google-sheets-parser.gs`
5. Нажмите **Ctrl+S** для сохранения
6. Дайте проекту название, например "JSON Parser"

## Использование

### Способ 1: Через меню (рекомендуется)

1. Вернитесь в Google Sheets
2. Обновите страницу (F5)
3. В верхнем меню появится новый пункт **"Парсер JSON"**
4. Доступные опции:
   - **Обработать данные** - основная функция парсинга
   - **Управление исключениями** - открыть лист для настройки исключений

### Способ 2: Через Apps Script

1. В редакторе Apps Script нажмите **Выполнить** (▶️)
2. При первом запуске потребуется дать разрешения
3. Результат появится в новом листе "Результаты"

## Управление исключениями

### Как добавить магазин в исключения:
1. Нажмите **Парсер JSON** → **Управление исключениями**
2. В листе "Исключения" добавьте название магазина в столбец A
3. В столбце B укажите "исключен"
4. При следующей обработке этот магазин будет пропущен

### Как вернуть магазин в обработку:
1. Откройте лист "Исключения"
2. Измените статус с "исключен" на "включен" или удалите строку

## Формат входных данных

- JSON данные должны быть в столбце A
- Каждая строка содержит один JSON объект
- Обязательные поля в JSON:
  - `shopName` - название магазина
  - `finance.price.EUR` - цена в евро
  - `finance.price.USD` - цена в долларах
  - `finance.price.RUB` - цена в рублях
  - `finance.payout` - статус выплаты (true/false, опционально)
  - `finance.info` - информация с ID платежей (опционально)

## Обработка значений

- Пустые значения (`""`) считаются как 0
- Значения с дефисом (`"-"`) считаются как 0
- `null` и `undefined` считаются как 0
- Только числовые значения добавляются к сумме

## Обработка статуса payout

- **Основные столбцы EUR/USD/RUB**: показывают **реальную статистику по всем заказам** независимо от статуса `payout`
- **Столбцы "Не выплачено"**: показывают только суммы с `"payout": false`
- **ID платежи**: учитываются только для записей с `"payout": true` или без указания статуса
- **Общая статистика**: разделена по статусам выплат

## ID платежей

- Скрипт автоматически ищет ID в формате `ID№xxxxx` в поле `finance.info`
- Например: `"info": "ВЫПЛАТА №3 ID№B4hT4V"` → найдет `ID№B4hT4V`
- Для каждого ID подсчитывается отдельная сумма по валютам
- Результаты выводятся в отдельных столбцах

## Формат результата

Результат выводится в лист "Результаты" в новом формате:

**Строка 8-9: Общая статистика**
```
ОБЩАЯ СТАТИСТИКА:
Выплачено (payout: true): 125.50€ | 85.00$ | 2500.00₽
Не выплачено (payout: false): 45.25€ | 30.00$ | 1000.00₽
```

**Строка 10+: Детальная таблица по магазинам**

| Магазин      | EUR    | USD    | RUB    | Не выплачено (EUR) | Не выплачено (USD) | Не выплачено (RUB) | ID Платежей 1 | ID Платежей 2 |
|--------------|--------|--------|--------|--------------------|--------------------|--------------------|---------------|---------------|
| ArmadaBoost  | 35.50€ | 35.00$ | 0.00₽  | 10.00€             | 5.00$              | 0.00₽              | ID№B4hT4V<br>25.50€ \| 30.00$ | |
| Boosthive.eu | 15.75€ | 20.50$ | 2000.00₽ | 0.00€            | 0.00$              | 500.00₽            | | |

**Пояснение столбцов:**
- **EUR/USD/RUB**: Общие суммы по всем заказам (включая выплаченные и не выплаченные)
- **Не выплачено**: Только суммы с `"payout": false`
- **ID Платежей**: Только для выплаченных заказов (`"payout": true`)

**Особенности форматирования:**
- Столбцы "Не выплачено" выделены розовым цветом
- ID платежи показывают суммы в несколько строк с переносом
- Общая статистика выделена желтым цветом

## Возможные ошибки

1. **"Не найдено валидных JSON данных"** - проверьте формат JSON в столбце A
2. **Ошибка парсинга** - проверьте синтаксис JSON (кавычки, скобки, запятые)
3. **Отсутствуют необходимые поля** - убедитесь что в JSON есть `shopName` и `finance.price`

## Примеры валидного JSON

### Базовый пример:
```json
{
  "shopName": "Boosthive.eu",
  "finance": {
    "price": {
      "EUR": 9.88,
      "USD": "-",
      "RUB": ""
    },
    "info": "ВЫПЛАТА №3"
  }
}
```

### Пример с ID платежа (выплачено):
```json
{
  "shopName": "ArmadaBoost",
  "finance": {
    "price": {
      "EUR": 15.50,
      "USD": "25.00",
      "RUB": ""
    },
    "info": "ВЫПЛАТА №5 ID№B4hT4V дополнительная информация",
    "payout": true
  }
}
```

### Пример с не выплаченной суммой:
```json
{
  "shopName": "ArmadaBoost",
  "finance": {
    "price": {
      "EUR": 10.00,
      "USD": "5.00",
      "RUB": ""
    },
    "info": "ВЫПЛАТА №6 ID№X7yZ9K ожидает обработки",
    "payout": false
  }
}
```

### Пример с несколькими ID:
```json
{
  "shopName": "MegaShop",
  "finance": {
    "price": {
      "EUR": 100.00,
      "USD": "",
      "RUB": "5000"
    },
    "info": "ID№ABC123 и ID№XYZ789 обработаны"
  }
}
```

## Дополнительные возможности

- Скрипт автоматически сортирует магазины по алфавиту
- Форматирует числа с символами валют
- Автоматически подбирает ширину столбцов
- Выделяет заголовки жирным шрифтом

## Поддержка

Если возникают ошибки, проверьте логи в Apps Script:
1. Перейдите в **Расширения** → **Apps Script**
2. Нажмите **Выполнить**
3. Посмотрите логи в нижней панели
