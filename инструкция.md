# Инструкция по использованию скрипта парсинга JSON в Google Sheets

## Что делает скрипт

Скрипт читает JSON данные из столбца A вашей Google Sheets таблицы, парсит их и создает сводную таблицу с суммами по валютам (EUR, USD, RUB) для каждого магазина.

## Установка скрипта

1. Откройте вашу Google Sheets таблицу
2. Перейдите в меню **Расширения** → **Apps Script**
3. Удалите весь код в редакторе (если есть)
4. Скопируйте и вставьте код из файла `google-sheets-parser.gs`
5. Нажмите **Ctrl+S** для сохранения
6. Дайте проекту название, например "JSON Parser"

## Использование

### Способ 1: Через меню (рекомендуется)

1. Вернитесь в Google Sheets
2. Обновите страницу (F5)
3. В верхнем меню появится новый пункт **"Парсер JSON"**
4. Нажмите **Парсер JSON** → **Обработать данные**
5. Скрипт создаст новый лист "Результаты" с итоговой таблицей

### Способ 2: Через Apps Script

1. В редакторе Apps Script нажмите **Выполнить** (▶️)
2. При первом запуске потребуется дать разрешения
3. Результат появится в новом листе "Результаты"

## Формат входных данных

- JSON данные должны быть в столбце A
- Каждая строка содержит один JSON объект
- Обязательные поля в JSON:
  - `shopName` - название магазина
  - `finance.price.EUR` - цена в евро
  - `finance.price.USD` - цена в долларах
  - `finance.price.RUB` - цена в рублях

## Обработка значений

- Пустые значения (`""`) считаются как 0
- Значения с дефисом (`"-"`) считаются как 0
- `null` и `undefined` считаются как 0
- Только числовые значения добавляются к сумме

## Формат результата

Результат выводится в лист "Результаты" в формате:

| Магазин      | EUR    | USD    | RUB    |
|--------------|--------|--------|--------|
| Boosthive.eu | 25.50€ | 30.00$ | 0.00₽  |
| Shop2.com    | 15.75€ | 20.50$ | 1500.00₽ |

## Возможные ошибки

1. **"Не найдено валидных JSON данных"** - проверьте формат JSON в столбце A
2. **Ошибка парсинга** - проверьте синтаксис JSON (кавычки, скобки, запятые)
3. **Отсутствуют необходимые поля** - убедитесь что в JSON есть `shopName` и `finance.price`

## Пример валидного JSON

```json
{
  "shopName": "Boosthive.eu",
  "finance": {
    "price": {
      "EUR": 9.88,
      "USD": "-",
      "RUB": ""
    }
  }
}
```

## Дополнительные возможности

- Скрипт автоматически сортирует магазины по алфавиту
- Форматирует числа с символами валют
- Автоматически подбирает ширину столбцов
- Выделяет заголовки жирным шрифтом

## Поддержка

Если возникают ошибки, проверьте логи в Apps Script:
1. Перейдите в **Расширения** → **Apps Script**
2. Нажмите **Выполнить**
3. Посмотрите логи в нижней панели
