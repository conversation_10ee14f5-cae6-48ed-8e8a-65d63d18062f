/**
 * Скрипт для парсинга JSON данных из Google Sheets
 * и подсчета сумм по валютам для каждого магазина
 */

function parseJsonAndCalculateSums() {
  // Получаем активную таблицу
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  
  // Получаем лист с исходными данными (предполагаем, что это первый лист)
  const sourceSheet = spreadsheet.getSheets()[0];
  
  // Создаем или получаем лист для результатов
  let resultSheet;
  try {
    resultSheet = spreadsheet.getSheetByName('Результаты');
  } catch (e) {
    resultSheet = spreadsheet.insertSheet('Результаты');
  }
  
  // Очищаем лист результатов
  resultSheet.clear();
  
  // Добавляем заголовки
  resultSheet.getRange(1, 1, 1, 4).setValues([['Магазин', 'EUR', 'USD', 'RUB']]);
  
  // Получаем данные из столбца A
  const lastRow = sourceSheet.getLastRow();
  if (lastRow < 1) {
    Logger.log('Нет данных для обработки');
    return;
  }
  
  const jsonData = sourceSheet.getRange(1, 1, lastRow, 1).getValues();
  
  // Объект для хранения сумм по магазинам
  const shopSums = {};
  
  // Обрабатываем каждую строку
  for (let i = 0; i < jsonData.length; i++) {
    const cellValue = jsonData[i][0];
    
    // Пропускаем пустые ячейки
    if (!cellValue || cellValue.toString().trim() === '') {
      continue;
    }
    
    try {
      // Парсим JSON
      const data = JSON.parse(cellValue.toString());
      
      // Проверяем наличие необходимых полей
      if (!data.shopName || !data.finance || !data.finance.price) {
        Logger.log(`Строка ${i + 1}: отсутствуют необходимые поля`);
        continue;
      }
      
      const shopName = data.shopName;
      const prices = data.finance.price;
      
      // Инициализируем магазин если его еще нет
      if (!shopSums[shopName]) {
        shopSums[shopName] = {
          EUR: 0,
          USD: 0,
          RUB: 0
        };
      }
      
      // Обрабатываем каждую валюту
      ['EUR', 'USD', 'RUB'].forEach(currency => {
        const priceValue = prices[currency];
        
        // Проверяем и конвертируем значение
        if (priceValue !== undefined && priceValue !== null && priceValue !== '' && priceValue !== '-') {
          const numericValue = parseFloat(priceValue);
          if (!isNaN(numericValue)) {
            shopSums[shopName][currency] += numericValue;
          }
        }
        // Если значение равно '-', '', null или undefined, считаем как 0 (ничего не добавляем)
      });
      
    } catch (error) {
      Logger.log(`Ошибка парсинга JSON в строке ${i + 1}: ${error.message}`);
      continue;
    }
  }
  
  // Подготавливаем данные для вывода
  const outputData = [];
  for (const shopName in shopSums) {
    outputData.push([
      shopName,
      shopSums[shopName].EUR,
      shopSums[shopName].USD,
      shopSums[shopName].RUB
    ]);
  }
  
  // Сортируем по названию магазина
  outputData.sort((a, b) => a[0].localeCompare(b[0]));
  
  // Записываем результаты
  if (outputData.length > 0) {
    resultSheet.getRange(2, 1, outputData.length, 4).setValues(outputData);
    
    // Форматируем заголовки
    const headerRange = resultSheet.getRange(1, 1, 1, 4);
    headerRange.setFontWeight('bold');
    headerRange.setBackground('#E8F0FE');
    
    // Форматируем числовые столбцы
    const eurRange = resultSheet.getRange(2, 2, outputData.length, 1);
    const usdRange = resultSheet.getRange(2, 3, outputData.length, 1);
    const rubRange = resultSheet.getRange(2, 4, outputData.length, 1);
    
    eurRange.setNumberFormat('0.00 "€"');
    usdRange.setNumberFormat('0.00 "$"');
    rubRange.setNumberFormat('0.00 "₽"');
    
    // Автоподбор ширины столбцов
    resultSheet.autoResizeColumns(1, 4);
    
    Logger.log(`Обработано магазинов: ${outputData.length}`);
    
    // Показываем сообщение пользователю
    SpreadsheetApp.getUi().alert(
      'Готово!', 
      `Данные успешно обработаны.\nОбработано магазинов: ${outputData.length}\nРезультаты выведены на лист "Результаты"`, 
      SpreadsheetApp.getUi().ButtonSet.OK
    );
  } else {
    Logger.log('Не найдено валидных данных для обработки');
    SpreadsheetApp.getUi().alert(
      'Внимание', 
      'Не найдено валидных JSON данных для обработки', 
      SpreadsheetApp.getUi().ButtonSet.OK
    );
  }
}

/**
 * Функция для создания меню в Google Sheets
 */
function onOpen() {
  const ui = SpreadsheetApp.getUi();
  ui.createMenu('Парсер JSON')
    .addItem('Обработать данные', 'parseJsonAndCalculateSums')
    .addToUi();
}

/**
 * Вспомогательная функция для тестирования с примером данных
 */
function testWithSampleData() {
  const sampleJson = `{
    "id": "Or_54_78533",
    "date": "2025-04-04T02:00:00.000Z",
    "shopNumber": "#223278",
    "shopName": "Boosthive.eu",
    "finance": {
      "price": {
        "EUR": 9.88,
        "USD": "",
        "RUB": ""
      },
      "info": "ВЫПЛАТА №3",
      "ourCheck": true,
      "payout": false
    }
  }`;
  
  Logger.log('Тестирование с примером данных...');
  
  try {
    const data = JSON.parse(sampleJson);
    Logger.log('JSON успешно распарсен');
    Logger.log('Магазин: ' + data.shopName);
    Logger.log('Цены: ' + JSON.stringify(data.finance.price));
  } catch (error) {
    Logger.log('Ошибка: ' + error.message);
  }
}
