/**
 * Скрипт для парсинга JSON данных из Google Sheets
 * и подсчета сумм по валютам для каждого магазина с поддержкой исключений и ID платежей
 */

// Список магазинов-исключений (можно редактировать)
const EXCLUDED_SHOPS = [
  // 'Пример исключенного магазина',
  // 'Другой исключенный магазин'
];

function parseJsonAndCalculateSums() {
  // Получаем активную таблицу
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();

  // Получаем лист с исходными данными (предполагаем, что это первый лист)
  const sourceSheet = spreadsheet.getSheets()[0];

  // Создаем или получаем лист для результатов
  let resultSheet;
  try {
    resultSheet = spreadsheet.getSheetByName('Результаты');
  } catch (e) {
    resultSheet = spreadsheet.insertSheet('Результаты');
  }

  // Создаем или получаем лист для управления исключениями
  let exclusionsSheet;
  try {
    exclusionsSheet = spreadsheet.getSheetByName('Исключения');
  } catch (e) {
    exclusionsSheet = spreadsheet.insertSheet('Исключения');
    setupExclusionsSheet(exclusionsSheet);
  }

  // Получаем актуальный список исключений
  const excludedShops = getExcludedShops(exclusionsSheet);

  // Очищаем лист результатов
  resultSheet.clear();

  // Получаем данные из столбца A
  const lastRow = sourceSheet.getLastRow();
  if (lastRow < 1) {
    Logger.log('Нет данных для обработки');
    return;
  }

  const jsonData = sourceSheet.getRange(1, 1, lastRow, 1).getValues();

  // Объекты для хранения данных
  const shopSums = {};
  const shopPaymentIds = {}; // Для хранения ID платежей по магазинам
  const shopPayoutFalseSums = {}; // Для хранения сумм с payout: false

  // Общие суммы для статистики
  const totalPayoutTrue = { EUR: 0, USD: 0, RUB: 0 };
  const totalPayoutFalse = { EUR: 0, USD: 0, RUB: 0 };

  // Обрабатываем каждую строку
  for (let i = 0; i < jsonData.length; i++) {
    const cellValue = jsonData[i][0];

    // Пропускаем пустые ячейки
    if (!cellValue || cellValue.toString().trim() === '') {
      continue;
    }

    try {
      // Парсим JSON
      const data = JSON.parse(cellValue.toString());

      // Проверяем наличие необходимых полей
      if (!data.shopName || !data.finance || !data.finance.price) {
        Logger.log(`Строка ${i + 1}: отсутствуют необходимые поля`);
        continue;
      }

      const shopName = data.shopName;

      // Проверяем, не в списке ли исключений
      if (excludedShops.includes(shopName)) {
        Logger.log(`Магазин ${shopName} в списке исключений, пропускаем`);
        continue;
      }

      const prices = data.finance.price;
      const payoutStatus = data.finance.payout; // true/false статус выплаты

      // Инициализируем магазин если его еще нет
      if (!shopSums[shopName]) {
        shopSums[shopName] = {
          EUR: 0,
          USD: 0,
          RUB: 0
        };
        shopPaymentIds[shopName] = {};
        shopPayoutFalseSums[shopName] = {
          EUR: 0,
          USD: 0,
          RUB: 0
        };
      }

      // Обрабатываем каждую валюту
      ['EUR', 'USD', 'RUB'].forEach(currency => {
        const priceValue = prices[currency];

        // Проверяем и конвертируем значение
        if (priceValue !== undefined && priceValue !== null && priceValue !== '' && priceValue !== '-') {
          const numericValue = parseFloat(priceValue);
          if (!isNaN(numericValue)) {
            // ВСЕГДА добавляем в основной счетчик (реальная статистика)
            shopSums[shopName][currency] += numericValue;

            // Разделяем по статусу payout для статистики и отдельных столбцов
            if (payoutStatus === false) {
              // Если payout: false, добавляем в отдельный счетчик
              shopPayoutFalseSums[shopName][currency] += numericValue;
              totalPayoutFalse[currency] += numericValue;
            } else {
              // Если payout: true или не указан
              totalPayoutTrue[currency] += numericValue;
            }
          }
        }
      });

      // Обрабатываем ID платежей из поля info (только если payout !== false)
      if (data.finance && data.finance.info && payoutStatus !== false) {
        const paymentIds = extractPaymentIds(data.finance.info);
        paymentIds.forEach(paymentId => {
          if (!shopPaymentIds[shopName][paymentId]) {
            shopPaymentIds[shopName][paymentId] = {
              EUR: 0,
              USD: 0,
              RUB: 0
            };
          }

          // Добавляем суммы к конкретному ID только если payout !== false
          ['EUR', 'USD', 'RUB'].forEach(currency => {
            const priceValue = prices[currency];
            if (priceValue !== undefined && priceValue !== null && priceValue !== '' && priceValue !== '-') {
              const numericValue = parseFloat(priceValue);
              if (!isNaN(numericValue)) {
                shopPaymentIds[shopName][paymentId][currency] += numericValue;
              }
            }
          });
        });
      }

    } catch (error) {
      Logger.log(`Ошибка парсинга JSON в строке ${i + 1}: ${error.message}`);
      continue;
    }
  }

  // Добавляем общую статистику на 8 строку
  resultSheet.getRange(8, 1).setValue('ОБЩАЯ СТАТИСТИКА:');
  resultSheet.getRange(8, 2).setValue(`Выплачено (payout: true): ${totalPayoutTrue.EUR.toFixed(2)}€ | ${totalPayoutTrue.USD.toFixed(2)}$ | ${totalPayoutTrue.RUB.toFixed(2)}₽`);
  resultSheet.getRange(9, 2).setValue(`Не выплачено (payout: false): ${totalPayoutFalse.EUR.toFixed(2)}€ | ${totalPayoutFalse.USD.toFixed(2)}$ | ${totalPayoutFalse.RUB.toFixed(2)}₽`);

  // Форматируем статистику
  resultSheet.getRange(8, 1, 2, 2).setFontWeight('bold');
  resultSheet.getRange(8, 1, 2, 2).setBackground('#FFF2CC');

  // Подготавливаем данные для вывода с учетом ID платежей
  const outputData = [];
  const headers = ['Магазин', 'EUR', 'USD', 'RUB', 'Не выплачено (EUR)', 'Не выплачено (USD)', 'Не выплачено (RUB)'];

  // Определяем максимальное количество столбцов для ID
  let maxPaymentIdColumns = 0;
  for (const shopName in shopPaymentIds) {
    const paymentIdCount = Object.keys(shopPaymentIds[shopName]).length;
    if (paymentIdCount > maxPaymentIdColumns) {
      maxPaymentIdColumns = paymentIdCount;
    }
  }

  // Добавляем заголовки для ID платежей
  for (let i = 1; i <= maxPaymentIdColumns; i++) {
    headers.push(`ID Платежей ${i}`);
  }

  // Устанавливаем заголовки на 10 строке
  resultSheet.getRange(10, 1, 1, headers.length).setValues([headers]);

  // Формируем данные для каждого магазина
  for (const shopName in shopSums) {
    const row = [
      shopName,
      shopSums[shopName].EUR,
      shopSums[shopName].USD,
      shopSums[shopName].RUB,
      shopPayoutFalseSums[shopName] ? shopPayoutFalseSums[shopName].EUR : 0,
      shopPayoutFalseSums[shopName] ? shopPayoutFalseSums[shopName].USD : 0,
      shopPayoutFalseSums[shopName] ? shopPayoutFalseSums[shopName].RUB : 0
    ];

    // Добавляем информацию о ID платежей
    const paymentIds = shopPaymentIds[shopName] || {};
    const paymentIdEntries = Object.entries(paymentIds);

    // Заполняем столбцы ID платежей
    for (let i = 0; i < maxPaymentIdColumns; i++) {
      if (i < paymentIdEntries.length) {
        const [paymentId, sums] = paymentIdEntries[i];
        const paymentInfo = formatPaymentIdInfo(paymentId, sums);
        row.push(paymentInfo);
      } else {
        row.push(''); // Пустая ячейка если нет ID
      }
    }

    outputData.push(row);
  }

  // Сортируем по названию магазина
  outputData.sort((a, b) => a[0].localeCompare(b[0]));

  // Записываем результаты начиная с 11 строки
  if (outputData.length > 0) {
    resultSheet.getRange(11, 1, outputData.length, headers.length).setValues(outputData);

    // Форматируем заголовки
    const headerRange = resultSheet.getRange(10, 1, 1, headers.length);
    headerRange.setFontWeight('bold');
    headerRange.setBackground('#E8F0FE');

    // Форматируем числовые столбцы (основные валюты)
    if (outputData.length > 0) {
      const eurRange = resultSheet.getRange(11, 2, outputData.length, 1);
      const usdRange = resultSheet.getRange(11, 3, outputData.length, 1);
      const rubRange = resultSheet.getRange(11, 4, outputData.length, 1);

      eurRange.setNumberFormat('0.00 "€"');
      usdRange.setNumberFormat('0.00 "$"');
      rubRange.setNumberFormat('0.00 "₽"');

      // Форматируем столбцы "Не выплачено"
      const eurFalseRange = resultSheet.getRange(11, 5, outputData.length, 1);
      const usdFalseRange = resultSheet.getRange(11, 6, outputData.length, 1);
      const rubFalseRange = resultSheet.getRange(11, 7, outputData.length, 1);

      eurFalseRange.setNumberFormat('0.00 "€"');
      usdFalseRange.setNumberFormat('0.00 "$"');
      rubFalseRange.setNumberFormat('0.00 "₽"');

      // Выделяем столбцы "Не выплачено" цветом
      resultSheet.getRange(11, 5, outputData.length, 3).setBackground('#FFE6E6');

      // Форматируем столбцы с ID платежей (перенос строк)
      for (let i = 8; i <= headers.length; i++) {
        const idRange = resultSheet.getRange(11, i, outputData.length, 1);
        idRange.setWrap(true);
        idRange.setVerticalAlignment('top');
      }
    }

    // Автоподбор ширины столбцов
    resultSheet.autoResizeColumns(1, headers.length);

    Logger.log(`Обработано магазинов: ${outputData.length}`);
    Logger.log(`Исключено магазинов: ${excludedShops.length}`);

    // Показываем сообщение пользователю
    SpreadsheetApp.getUi().alert(
      'Готово!',
      `Данные успешно обработаны.\nОбработано магазинов: ${outputData.length}\nИсключено магазинов: ${excludedShops.length}\nРезультаты выведены на лист "Результаты"`,
      SpreadsheetApp.getUi().ButtonSet.OK
    );
  } else {
    Logger.log('Не найдено валидных данных для обработки');
    SpreadsheetApp.getUi().alert(
      'Внимание',
      'Не найдено валидных JSON данных для обработки',
      SpreadsheetApp.getUi().ButtonSet.OK
    );
  }
}

/**
 * Вспомогательные функции
 */

// Функция для извлечения ID платежей из строки info
function extractPaymentIds(infoString) {
  const paymentIds = [];
  if (!infoString) return paymentIds;

  // Ищем все вхождения ID№ с последующими символами
  const regex = /ID№([A-Za-z0-9]+)/g;
  let match;

  while ((match = regex.exec(infoString)) !== null) {
    paymentIds.push('ID№' + match[1]);
  }

  return paymentIds;
}

// Функция для форматирования информации о платежном ID
function formatPaymentIdInfo(paymentId, sums) {
  const parts = [];

  if (sums.EUR > 0) parts.push(`${sums.EUR.toFixed(2)}€`);
  if (sums.USD > 0) parts.push(`${sums.USD.toFixed(2)}$`);
  if (sums.RUB > 0) parts.push(`${sums.RUB.toFixed(2)}₽`);

  if (parts.length === 0) return paymentId + '\n0.00';

  return paymentId + '\n' + parts.join(' | ');
}

// Функция для настройки листа исключений
function setupExclusionsSheet(sheet) {
  sheet.clear();
  sheet.getRange(1, 1).setValue('Исключенные магазины');
  sheet.getRange(1, 2).setValue('Статус');

  // Заголовки
  const headerRange = sheet.getRange(1, 1, 1, 2);
  headerRange.setFontWeight('bold');
  headerRange.setBackground('#FFE6E6');

  // Инструкция
  sheet.getRange(3, 1).setValue('Инструкция:');
  sheet.getRange(4, 1).setValue('1. Добавьте названия магазинов в столбец A');
  sheet.getRange(5, 1).setValue('2. В столбце B укажите "исключен" или "включен"');
  sheet.getRange(6, 1).setValue('3. Магазины со статусом "исключен" не будут обрабатываться');

  sheet.autoResizeColumns(1, 2);
}

// Функция для получения списка исключенных магазинов
function getExcludedShops(exclusionsSheet) {
  const excludedShops = [];

  try {
    const lastRow = exclusionsSheet.getLastRow();
    if (lastRow <= 1) return excludedShops; // Нет данных кроме заголовка

    const data = exclusionsSheet.getRange(2, 1, lastRow - 1, 2).getValues();

    for (let i = 0; i < data.length; i++) {
      const shopName = data[i][0];
      const status = data[i][1];

      if (shopName && status && status.toString().toLowerCase().includes('исключен')) {
        excludedShops.push(shopName.toString());
      }
    }
  } catch (error) {
    Logger.log('Ошибка при чтении списка исключений: ' + error.message);
  }

  return excludedShops;
}

/**
 * Функция для создания меню в Google Sheets
 */
function onOpen() {
  const ui = SpreadsheetApp.getUi();
  ui.createMenu('Парсер JSON')
    .addItem('Обработать данные', 'parseJsonAndCalculateSums')
    .addSeparator()
    .addItem('Управление исключениями', 'openExclusionsSheet')
    .addToUi();
}

/**
 * Функция для открытия листа исключений
 */
function openExclusionsSheet() {
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();

  let exclusionsSheet;
  try {
    exclusionsSheet = spreadsheet.getSheetByName('Исключения');
  } catch (e) {
    exclusionsSheet = spreadsheet.insertSheet('Исключения');
    setupExclusionsSheet(exclusionsSheet);
  }

  exclusionsSheet.activate();

  SpreadsheetApp.getUi().alert(
    'Управление исключениями',
    'Лист "Исключения" открыт.\n\nДобавьте названия магазинов в столбец A и укажите статус "исключен" в столбце B для магазинов, которые не нужно обрабатывать.',
    SpreadsheetApp.getUi().ButtonSet.OK
  );
}

/**
 * Вспомогательная функция для тестирования с примером данных
 */
function testWithSampleData() {
  const sampleJson = `{
    "id": "Or_54_78533",
    "date": "2025-04-04T02:00:00.000Z",
    "shopNumber": "#223278",
    "shopName": "ArmadaBoost",
    "finance": {
      "price": {
        "EUR": 9.88,
        "USD": "15.50",
        "RUB": ""
      },
      "info": "ВЫПЛАТА №3 ID№B4hT4V",
      "ourCheck": true,
      "payout": false
    }
  }`;

  Logger.log('Тестирование с примером данных...');

  try {
    const data = JSON.parse(sampleJson);
    Logger.log('JSON успешно распарсен');
    Logger.log('Магазин: ' + data.shopName);
    Logger.log('Цены: ' + JSON.stringify(data.finance.price));
    Logger.log('Info: ' + data.finance.info);

    // Тестируем извлечение ID
    const paymentIds = extractPaymentIds(data.finance.info);
    Logger.log('Найденные ID платежей: ' + JSON.stringify(paymentIds));

  } catch (error) {
    Logger.log('Ошибка: ' + error.message);
  }
}

/**
 * Функция для добавления магазина в исключения
 */
function addShopToExclusions(shopName) {
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();

  let exclusionsSheet;
  try {
    exclusionsSheet = spreadsheet.getSheetByName('Исключения');
  } catch (e) {
    exclusionsSheet = spreadsheet.insertSheet('Исключения');
    setupExclusionsSheet(exclusionsSheet);
  }

  const lastRow = exclusionsSheet.getLastRow();
  exclusionsSheet.getRange(lastRow + 1, 1).setValue(shopName);
  exclusionsSheet.getRange(lastRow + 1, 2).setValue('исключен');

  Logger.log(`Магазин "${shopName}" добавлен в исключения`);
}

/**
 * Функция для получения всех уникальных магазинов из данных
 */
function getAllShopsFromData() {
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  const sourceSheet = spreadsheet.getSheets()[0];

  const lastRow = sourceSheet.getLastRow();
  if (lastRow < 1) return [];

  const jsonData = sourceSheet.getRange(1, 1, lastRow, 1).getValues();
  const shops = new Set();

  for (let i = 0; i < jsonData.length; i++) {
    const cellValue = jsonData[i][0];

    if (!cellValue || cellValue.toString().trim() === '') continue;

    try {
      const data = JSON.parse(cellValue.toString());
      if (data.shopName) {
        shops.add(data.shopName);
      }
    } catch (error) {
      continue;
    }
  }

  return Array.from(shops).sort();
}
